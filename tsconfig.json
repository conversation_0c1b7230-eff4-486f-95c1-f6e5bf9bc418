{
  "compilerOptions": {
    "target": "esnext",
    "jsx": "preserve",
    "lib": ["esnext", "dom"],
    "useDefineForClassFields": true,
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "bundler",
    "paths": {
      "~/*": ["src/*"],
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "vite/client",
      "unplugin-vue-router/client"
    ],
    // 放宽 TypeScript 严格检查
    "strict": false,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "sourceMap": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "vueCompilerOptions": {
    "target": 3
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]
}
