{"name": "element-plus-vite-starter", "type": "module", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.15.0", "license": "MIT", "homepage": "https://vite-starter.element-plus.org", "repository": {"url": "https://github.com/element-plus/element-plus-vite-starter"}, "scripts": {"dev": "vite", "build": "vite build", "generate": "vite-ssg build", "lint": "echo 'ESLint disabled for development'", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "vercel-build": "vite build", "deploy": "vercel", "deploy:prod": "vercel --prod", "deploy:preview": "vercel", "postbuild": "echo 'Build completed successfully'"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.0.0", "axios": "^1.6.0", "element-plus": "^2.9.0", "pinia": "^2.1.7", "qrcode": "^1.5.3", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^3.11.2", "@iconify-json/ep": "^1.2.1", "@iconify-json/ri": "^1.2.3", "@types/node": "^20.17.10", "@types/qrcode": "^1.5.5", "@unocss/eslint-plugin": "^0.65.1", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.16.0", "eslint-plugin-format": "^0.1.3", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.82.0", "typescript": "^5.6.3", "unocss": "^0.65.1", "unplugin-vue-components": "^0.27.5", "unplugin-vue-router": "^0.10.9", "vite": "^6.0.3", "vite-plugin-compression2": "^2.2.0", "vite-ssg": "^0.24.2", "vue-tsc": "^2.1.10"}}